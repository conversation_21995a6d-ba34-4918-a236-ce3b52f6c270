#!/usr/bin/env python3
"""
Ultimate PDF Layout Extractor with Advanced Table Detection
Author: Dr<PERSON> <PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Description: Most advanced PDF extraction with perfect table layout preservation
Created: 2025-06-21

This script implements multiple advanced strategies:
- Multi-method table detection and extraction
- Layout analysis with coordinate-based positioning
- Text block analysis for proper spacing
- Advanced table formatting with alignment
- Comprehensive verification and validation
"""

import os
import glob
import logging
from pathlib import Path
from datetime import datetime
import re
import traceback
import json

# Import PDF processing libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimatePDFExtractor:
    """
    Ultimate PDF extractor with advanced table detection and perfect layout preservation.
    """
    
    def __init__(self, output_filename="QS_WUR_Perfect_Content.txt"):
        self.output_filename = output_filename
        self.extraction_stats = {
            'total_files': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_pages': 0,
            'tables_extracted': 0,
            'text_blocks_processed': 0,
            'extraction_methods_used': []
        }
        self.verification_log = []
    
    def find_pdf_files(self, directory="."):
        """Find all PDF files in the specified directory."""
        pdf_files = glob.glob(os.path.join(directory, "*.pdf"))
        pdf_files.sort()
        logger.info(f"Found {len(pdf_files)} PDF files")
        return pdf_files
    
    def analyze_text_layout(self, page):
        """
        Analyze text layout using coordinate-based positioning for perfect alignment.
        """
        try:
            # Get all text with coordinates
            words = page.extract_words(x_tolerance=3, y_tolerance=3)
            
            if not words:
                return ""
            
            # Group words by lines based on y-coordinates
            lines = {}
            for word in words:
                y = round(word['top'], 1)
                if y not in lines:
                    lines[y] = []
                lines[y].append(word)
            
            # Sort lines by y-coordinate (top to bottom)
            sorted_lines = []
            for y in sorted(lines.keys()):
                # Sort words in each line by x-coordinate (left to right)
                line_words = sorted(lines[y], key=lambda w: w['x0'])
                sorted_lines.append((y, line_words))
            
            # Reconstruct text with proper spacing
            text_lines = []
            for y, words_in_line in sorted_lines:
                if not words_in_line:
                    continue
                
                # Calculate spacing between words
                line_text = ""
                prev_x1 = 0
                
                for i, word in enumerate(words_in_line):
                    if i == 0:
                        # First word - add leading spaces based on x0
                        leading_spaces = max(0, int(word['x0'] / 6))  # Approximate character width
                        line_text = " " * leading_spaces + word['text']
                    else:
                        # Calculate spacing between words
                        space_width = word['x0'] - prev_x1
                        num_spaces = max(1, int(space_width / 6))  # Minimum 1 space
                        line_text += " " * num_spaces + word['text']
                    
                    prev_x1 = word['x1']
                
                text_lines.append(line_text.rstrip())
            
            return '\n'.join(text_lines)
            
        except Exception as e:
            logger.warning(f"Text layout analysis failed: {str(e)}")
            return ""
    
    def detect_and_extract_tables_advanced(self, page):
        """
        Advanced table detection using multiple strategies and coordinate analysis.
        """
        tables_content = []
        
        try:
            # Strategy 1: pdfplumber table extraction with multiple settings
            table_settings_list = [
                {
                    "vertical_strategy": "lines",
                    "horizontal_strategy": "lines",
                    "snap_tolerance": 3,
                    "join_tolerance": 3,
                    "edge_min_length": 3,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1,
                    "intersection_tolerance": 3,
                    "text_tolerance": 3,
                    "text_x_tolerance": 3,
                    "text_y_tolerance": 3
                },
                {
                    "vertical_strategy": "text",
                    "horizontal_strategy": "text",
                    "snap_tolerance": 5,
                    "join_tolerance": 5,
                    "edge_min_length": 5,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1,
                    "intersection_tolerance": 5,
                    "text_tolerance": 5,
                    "text_x_tolerance": 5,
                    "text_y_tolerance": 5
                },
                {
                    "vertical_strategy": "explicit",
                    "horizontal_strategy": "explicit",
                    "explicit_vertical_lines": [],
                    "explicit_horizontal_lines": [],
                    "snap_tolerance": 2,
                    "join_tolerance": 2,
                    "edge_min_length": 10,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1
                }
            ]
            
            tables = None
            successful_strategy = None
            
            for i, settings in enumerate(table_settings_list):
                try:
                    tables = page.extract_tables(table_settings=settings)
                    if tables and len(tables) > 0:
                        successful_strategy = f"Strategy {i+1}"
                        break
                except Exception as e:
                    logger.debug(f"Table extraction strategy {i+1} failed: {str(e)}")
                    continue
            
            # Strategy 2: Manual table detection using text patterns
            if not tables or len(tables) == 0:
                tables = self.detect_tables_by_pattern(page)
                if tables:
                    successful_strategy = "Pattern Detection"
            
            # Process extracted tables
            if tables:
                for table_num, table in enumerate(tables, 1):
                    if table and len(table) > 0:
                        tables_content.append(f"\n{'='*80}")
                        tables_content.append(f"TABLE {table_num} - DETECTED USING {successful_strategy}")
                        tables_content.append(f"{'='*80}")
                        
                        # Clean and format table
                        formatted_table = self.format_table_perfectly(table)
                        tables_content.extend(formatted_table)
                        
                        tables_content.append(f"{'='*80}")
                        tables_content.append(f"END OF TABLE {table_num}")
                        tables_content.append(f"{'='*80}\n")
                        
                        self.extraction_stats['tables_extracted'] += 1
                        logger.info(f"Successfully extracted table {table_num} using {successful_strategy}")
            
        except Exception as e:
            logger.warning(f"Advanced table extraction failed: {str(e)}")
        
        return '\n'.join(tables_content)
    
    def detect_tables_by_pattern(self, page):
        """
        Detect tables by analyzing text patterns and alignment.
        """
        try:
            # Get text with layout preservation
            text = page.extract_text(layout=True, x_tolerance=3, y_tolerance=3)
            if not text:
                return []
            
            lines = text.split('\n')
            tables = []
            current_table = []
            in_table = False
            
            for line in lines:
                # Skip empty lines
                if not line.strip():
                    if in_table and current_table:
                        # End of table
                        tables.append(current_table)
                        current_table = []
                        in_table = False
                    continue
                
                # Detect table-like patterns
                # Look for lines with multiple columns separated by spaces
                if self.is_table_row(line):
                    if not in_table:
                        in_table = True
                    current_table.append(self.parse_table_row(line))
                else:
                    if in_table and current_table:
                        # End of table
                        tables.append(current_table)
                        current_table = []
                        in_table = False
            
            # Don't forget the last table
            if in_table and current_table:
                tables.append(current_table)
            
            return tables
            
        except Exception as e:
            logger.warning(f"Pattern-based table detection failed: {str(e)}")
            return []
    
    def is_table_row(self, line):
        """
        Determine if a line looks like a table row.
        """
        # Remove leading/trailing spaces
        line = line.strip()
        
        # Must have content
        if not line:
            return False
        
        # Look for multiple words separated by significant spaces
        words = line.split()
        if len(words) < 2:
            return False
        
        # Check for consistent spacing patterns
        # Look for 3+ consecutive spaces (indicating column separation)
        if re.search(r'\s{3,}', line):
            return True
        
        # Look for tab characters
        if '\t' in line:
            return True
        
        # Look for percentage signs, numbers, or other table-like content
        if re.search(r'\d+%|\d+\.\d+|\d+,\d+', line):
            return True
        
        return False
    
    def parse_table_row(self, line):
        """
        Parse a table row into columns.
        """
        # Split by multiple spaces or tabs
        columns = re.split(r'\s{3,}|\t+', line.strip())
        
        # Clean up columns
        cleaned_columns = []
        for col in columns:
            col = col.strip()
            if col:  # Only add non-empty columns
                cleaned_columns.append(col)
        
        return cleaned_columns
    
    def format_table_perfectly(self, table):
        """
        Format table with perfect alignment and spacing.
        """
        if not table or len(table) == 0:
            return []
        
        # Clean the table data
        cleaned_table = []
        for row in table:
            if row:  # Skip empty rows
                cleaned_row = []
                for cell in row:
                    cell_text = str(cell) if cell is not None else ""
                    cleaned_row.append(cell_text.strip())
                if any(cell for cell in cleaned_row):  # Only add rows with content
                    cleaned_table.append(cleaned_row)
        
        if not cleaned_table:
            return []
        
        # Calculate maximum width for each column
        max_cols = max(len(row) for row in cleaned_table)
        col_widths = [0] * max_cols
        
        # Pad rows to have the same number of columns
        for row in cleaned_table:
            while len(row) < max_cols:
                row.append("")
        
        # Calculate column widths
        for row in cleaned_table:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        # Format the table
        formatted_lines = []
        
        for row_idx, row in enumerate(cleaned_table):
            # Format each cell with proper padding
            formatted_cells = []
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    # Left-align text, pad to column width
                    formatted_cell = str(cell).ljust(col_widths[i])
                    formatted_cells.append(formatted_cell)
                else:
                    formatted_cells.append(str(cell))
            
            # Join cells with separators
            formatted_row = " | ".join(formatted_cells)
            formatted_lines.append(formatted_row)
            
            # Add separator line after header (first row)
            if row_idx == 0 and len(cleaned_table) > 1:
                separator_parts = []
                for width in col_widths:
                    separator_parts.append("-" * width)
                separator = " | ".join(separator_parts)
                formatted_lines.append(separator)
        
        return formatted_lines

    def extract_with_ultimate_precision(self, pdf_path):
        """
        Ultimate precision extraction combining all methods.
        """
        if not PDFPLUMBER_AVAILABLE:
            return None, "pdfplumber not available"

        try:
            content_parts = []

            with pdfplumber.open(pdf_path) as pdf:
                logger.info(f"Processing {len(pdf.pages)} pages with ultimate precision")

                for page_num, page in enumerate(pdf.pages, 1):
                    content_parts.append(f"\n{'='*100}")
                    content_parts.append(f"PAGE {page_num}")
                    content_parts.append(f"{'='*100}")

                    # Get page dimensions
                    page_width = page.width
                    page_height = page.height
                    content_parts.append(f"Page dimensions: {page_width:.1f} x {page_height:.1f}")
                    content_parts.append("")

                    # Method 1: Advanced table detection first
                    table_content = self.detect_and_extract_tables_advanced(page)

                    # Method 2: Coordinate-based text layout analysis
                    layout_text = self.analyze_text_layout(page)

                    # Method 3: Standard text extraction as fallback
                    if not layout_text.strip():
                        layout_text = page.extract_text(layout=True, x_tolerance=3, y_tolerance=3)

                    # Combine results intelligently
                    if table_content.strip():
                        content_parts.append("EXTRACTED TABLES:")
                        content_parts.append(table_content)
                        content_parts.append("\nFULL PAGE CONTENT WITH LAYOUT:")

                    if layout_text:
                        content_parts.append(layout_text)
                    else:
                        content_parts.append("[No readable text found on this page]")

                    content_parts.append(f"\n{'='*100}")
                    content_parts.append("")

                    self.extraction_stats['total_pages'] += 1
                    self.extraction_stats['text_blocks_processed'] += 1

            return '\n'.join(content_parts), "success"

        except Exception as e:
            logger.error(f"Ultimate precision extraction failed: {str(e)}")
            return None, f"Ultimate extraction error: {str(e)}"

    def verify_extraction_quality(self, content, pdf_path):
        """
        Verify the quality of extraction and log issues.
        """
        verification_results = {
            'file': os.path.basename(pdf_path),
            'content_length': len(content),
            'line_count': len(content.split('\n')),
            'table_count': content.count('TABLE') // 2,  # Approximate
            'issues': []
        }

        # Check for common issues
        if len(content) < 100:
            verification_results['issues'].append("Content too short - possible extraction failure")

        if 'PAGE 1' not in content:
            verification_results['issues'].append("Missing page structure")

        if verification_results['table_count'] == 0 and 'table' in pdf_path.lower():
            verification_results['issues'].append("No tables detected in file that likely contains tables")

        # Check for garbled text
        garbled_ratio = len(re.findall(r'[^\w\s\-.,;:()%/]', content)) / len(content) if content else 0
        if garbled_ratio > 0.1:
            verification_results['issues'].append(f"High ratio of special characters ({garbled_ratio:.2%}) - possible encoding issues")

        self.verification_log.append(verification_results)

        if verification_results['issues']:
            logger.warning(f"Verification issues for {verification_results['file']}: {verification_results['issues']}")
        else:
            logger.info(f"Verification passed for {verification_results['file']}")

        return verification_results

    def process_all_pdfs_ultimate(self, directory="."):
        """
        Process all PDFs with ultimate precision and verification.
        """
        pdf_files = self.find_pdf_files(directory)
        self.extraction_stats['total_files'] = len(pdf_files)

        if not pdf_files:
            logger.warning("No PDF files found in the directory")
            return False

        # Create the combined content
        content_parts = []

        # Add comprehensive header
        content_parts.append("=" * 120)
        content_parts.append("QS WORLD UNIVERSITY RANKINGS - ULTIMATE PRECISION EXTRACTION")
        content_parts.append("=" * 120)
        content_parts.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_parts.append(f"Total PDF files processed: {len(pdf_files)}")
        content_parts.append(f"Extraction method: Ultimate precision with advanced table detection")
        content_parts.append(f"Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        content_parts.append(f"Contact: <EMAIL> / <EMAIL>")
        content_parts.append("=" * 120)
        content_parts.append("")

        # Process each PDF file
        for pdf_index, pdf_path in enumerate(pdf_files, 1):
            filename = os.path.basename(pdf_path)
            logger.info(f"Processing {pdf_index}/{len(pdf_files)}: {filename}")

            # Add file header
            content_parts.append("\n" + "#" * 120)
            content_parts.append(f"DOCUMENT {pdf_index}: {filename}")
            content_parts.append("#" * 120)
            content_parts.append(f"Source File: {filename}")
            content_parts.append(f"Full Path: {pdf_path}")
            content_parts.append(f"Processing Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content_parts.append("#" * 120)

            # Extract content with ultimate precision
            content, status = self.extract_with_ultimate_precision(pdf_path)

            if content and status == "success":
                # Verify extraction quality
                verification = self.verify_extraction_quality(content, pdf_path)

                content_parts.append(content)
                self.extraction_stats['successful_extractions'] += 1

                if "pdfplumber" not in self.extraction_stats['extraction_methods_used']:
                    self.extraction_stats['extraction_methods_used'].append("pdfplumber")

                logger.info(f"Successfully processed {filename} - {verification['line_count']} lines, {verification['table_count']} tables")
            else:
                error_content = f"""
ERROR: FAILED TO EXTRACT CONTENT FROM {filename}
{'='*80}
Extraction method failed: {status}
Please check the file manually or try alternative tools.
File may be corrupted, password-protected, or use unsupported formatting.
{'='*80}
"""
                content_parts.append(error_content)
                self.extraction_stats['failed_extractions'] += 1
                logger.error(f"Failed to process {filename}: {status}")

            # Add document separator
            content_parts.append("\n" + "#" * 120)
            content_parts.append("")

        # Add comprehensive statistics
        content_parts.append("\n" + "=" * 120)
        content_parts.append("EXTRACTION STATISTICS AND VERIFICATION SUMMARY")
        content_parts.append("=" * 120)
        content_parts.append(f"Total files found: {self.extraction_stats['total_files']}")
        content_parts.append(f"Successfully extracted: {self.extraction_stats['successful_extractions']}")
        content_parts.append(f"Failed extractions: {self.extraction_stats['failed_extractions']}")
        content_parts.append(f"Total pages processed: {self.extraction_stats['total_pages']}")
        content_parts.append(f"Tables extracted: {self.extraction_stats['tables_extracted']}")
        content_parts.append(f"Text blocks processed: {self.extraction_stats['text_blocks_processed']}")
        content_parts.append(f"Extraction methods used: {', '.join(self.extraction_stats['extraction_methods_used'])}")
        content_parts.append(f"Success rate: {(self.extraction_stats['successful_extractions']/self.extraction_stats['total_files']*100):.1f}%")
        content_parts.append("")

        # Add verification summary
        content_parts.append("VERIFICATION RESULTS:")
        content_parts.append("-" * 60)
        for result in self.verification_log:
            content_parts.append(f"File: {result['file']}")
            content_parts.append(f"  Content length: {result['content_length']:,} characters")
            content_parts.append(f"  Line count: {result['line_count']:,}")
            content_parts.append(f"  Tables detected: {result['table_count']}")
            if result['issues']:
                content_parts.append(f"  Issues: {'; '.join(result['issues'])}")
            else:
                content_parts.append("  Status: ✅ All checks passed")
            content_parts.append("")

        content_parts.append("Generated by: Ultimate PDF Layout Extractor")
        content_parts.append("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        content_parts.append("Contact: <EMAIL> / <EMAIL>")
        content_parts.append("=" * 120)

        # Write to file
        try:
            final_content = '\n'.join(content_parts)
            with open(self.output_filename, 'w', encoding='utf-8') as f:
                f.write(final_content)

            logger.info(f"Successfully created {self.output_filename}")
            logger.info(f"Final file size: {len(final_content):,} characters")
            logger.info(f"Extraction Statistics: {self.extraction_stats}")
            return True

        except Exception as e:
            logger.error(f"Failed to write output file: {str(e)}")
            return False

def main():
    """Main function to run the ultimate PDF extraction process."""
    print("=" * 120)
    print("ULTIMATE PDF LAYOUT EXTRACTOR WITH ADVANCED TABLE DETECTION")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("Focus: Perfect layout preservation with comprehensive table extraction")
    print("=" * 120)

    # Initialize extractor
    extractor = UltimatePDFExtractor("QS_WUR_Perfect_Content.txt")

    # Process all PDFs with ultimate precision
    success = extractor.process_all_pdfs_ultimate()

    if success:
        print(f"\n✅ Successfully created {extractor.output_filename}")
        print(f"📊 Ultimate Extraction Statistics:")
        for key, value in extractor.extraction_stats.items():
            print(f"   - {key}: {value}")

        print(f"\n🔍 Verification Summary:")
        total_issues = sum(len(result['issues']) for result in extractor.verification_log)
        print(f"   - Files verified: {len(extractor.verification_log)}")
        print(f"   - Total issues found: {total_issues}")
        print(f"   - Files with issues: {sum(1 for result in extractor.verification_log if result['issues'])}")

        print(f"\n📄 Output file ready for AI tools with perfect layout preservation")
    else:
        print("\n❌ Failed to create the ultimate content file")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())

#!/usr/bin/env python3
"""
QS Methodology Visualizations for SIU Leadership Presentation
Author: Dr. <PERSON><PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Description: Creates professional visualizations for QS methodology presentation
Created: 2025-06-21
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# Set professional styling
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Color scheme for SIU branding
SIU_COLORS = {
    'primary': '#1f4e79',      # Deep blue
    'secondary': '#ff6b35',    # Orange
    'accent': '#2e8b57',       # Sea green
    'neutral': '#708090',      # Slate gray
    'light': '#f0f8ff',        # Alice blue
    'success': '#28a745',      # Green
    'warning': '#ffc107',      # Amber
    'danger': '#dc3545'        # Red
}

def create_qs_indicators_chart():
    """Create QS 2026 Indicators Weight Distribution Chart"""
    
    # Data for QS 2026 indicators
    indicators = [
        'Academic Reputation', 'Citations per Faculty', 'Employer Reputation',
        'Faculty Student Ratio', 'International Faculty', 'International Students',
        'International Research Network', 'Employment Outcomes', 'Sustainability'
    ]
    
    weights = [30, 20, 15, 10, 5, 5, 5, 5, 5]
    
    # Create figure
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Pie chart
    colors = [SIU_COLORS['primary'], SIU_COLORS['secondary'], SIU_COLORS['accent'],
              SIU_COLORS['neutral'], SIU_COLORS['success'], SIU_COLORS['warning'],
              '#9370db', '#ff69b4', '#20b2aa']
    
    wedges, texts, autotexts = ax1.pie(weights, labels=indicators, autopct='%1.1f%%',
                                       colors=colors, startangle=90, textprops={'fontsize': 10})
    
    ax1.set_title('QS World University Rankings 2026\nIndicator Weights Distribution', 
                  fontsize=16, fontweight='bold', pad=20)
    
    # Bar chart
    bars = ax2.barh(indicators, weights, color=colors)
    ax2.set_xlabel('Weight (%)', fontsize=12, fontweight='bold')
    ax2.set_title('QS 2026 Indicators\nWeight Breakdown', fontsize=16, fontweight='bold')
    ax2.grid(axis='x', alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, weight) in enumerate(zip(bars, weights)):
        ax2.text(weight + 0.5, i, f'{weight}%', va='center', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('QS_2026_Indicators_Distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_classification_comparison():
    """Create Institution Classification Comparison Chart"""
    
    # Research thresholds data
    classifications = ['FC\n(Full Comprehensive)', 'CO\n(Comprehensive)', 
                      'FO\n(Focused)', 'SP\n(Specialist)']
    
    # Research thresholds for Large (L) institutions
    vh_thresholds = [10000, 7000, 3500, 'Variable']
    hi_thresholds = [3000, 2000, 1000, 'Variable']
    
    # Create figure
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Classification types chart
    x = np.arange(len(classifications))
    width = 0.35
    
    # Convert 'Variable' to 0 for plotting, will handle separately
    vh_plot = [10000, 7000, 3500, 0]
    hi_plot = [3000, 2000, 1000, 0]
    
    bars1 = ax1.bar(x - width/2, vh_plot, width, label='Very High (VH)', 
                    color=SIU_COLORS['primary'], alpha=0.8)
    bars2 = ax1.bar(x + width/2, hi_plot, width, label='High (HI)', 
                    color=SIU_COLORS['secondary'], alpha=0.8)
    
    ax1.set_xlabel('Institution Classification', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Research Threshold (5-year Scopus Publications)', fontsize=12, fontweight='bold')
    ax1.set_title('QS Research Intensity Thresholds by Classification\n(Large Institutions)', 
                  fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(classifications)
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # Add value labels
    for bar in bars1:
        height = bar.get_height()
        if height > 0:
            ax1.text(bar.get_x() + bar.get_width()/2., height + 100,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        if height > 0:
            ax1.text(bar.get_x() + bar.get_width()/2., height + 100,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
    
    # Add special note for SP
    ax1.text(3, 500, 'Variable\n(Subject-specific)', ha='center', va='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor=SIU_COLORS['warning'], alpha=0.7))
    
    # Faculty areas coverage chart
    faculty_areas = ['Arts &\nHumanities', 'Engineering &\nTechnology', 
                    'Life Sciences &\nMedicine', 'Natural\nSciences', 
                    'Social Sciences &\nManagement', 'Medical\nSchool']
    
    # Coverage matrix (1 = covered, 0.5 = partial, 0 = not covered)
    coverage_data = {
        'FC': [1, 1, 1, 1, 1, 1],
        'CO': [1, 1, 1, 1, 1, 0],
        'FO': [0.5, 0.5, 0.5, 0.5, 0.5, 0],  # 3-4 areas
        'SP': [0.3, 0.3, 0.3, 0.3, 0.3, 0]   # ≤2 areas
    }
    
    # Create heatmap
    coverage_df = pd.DataFrame(coverage_data, index=faculty_areas)
    
    im = ax2.imshow(coverage_df.values, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    # Set ticks and labels
    ax2.set_xticks(np.arange(len(classifications)))
    ax2.set_yticks(np.arange(len(faculty_areas)))
    ax2.set_xticklabels(['FC', 'CO', 'FO', 'SP'])
    ax2.set_yticklabels(faculty_areas)
    
    # Add text annotations
    for i in range(len(faculty_areas)):
        for j in range(len(classifications)):
            value = coverage_df.iloc[i, j]
            if value == 1:
                text = 'Y'
                color = 'white'
            elif value > 0:
                text = 'P'
                color = 'black'
            else:
                text = 'N'
                color = 'white'
            ax2.text(j, i, text, ha="center", va="center", color=color, fontsize=16, fontweight='bold')
    
    ax2.set_title('Faculty Area Coverage by Classification', fontsize=14, fontweight='bold')
    
    # Add colorbar
    cbar = plt.colorbar(im, ax=ax2, shrink=0.6)
    cbar.set_label('Coverage Level', rotation=270, labelpad=15)
    
    plt.tight_layout()
    plt.savefig('QS_Classification_Comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_siu_transformation_chart():
    """Create SIU FO to CO Transformation Impact Chart"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Research Threshold Comparison
    categories = ['Very High (VH)', 'High (HI)', 'Medium (MD)', 'Low (LO)']
    fo_thresholds = [2500, 750, 100, 0]  # FO thresholds for Medium (M) size
    co_thresholds = [2500, 750, 100, 0]  # CO thresholds for Medium (M) size
    
    x = np.arange(len(categories))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, fo_thresholds, width, label='FO (Previous)', 
                    color=SIU_COLORS['neutral'], alpha=0.7)
    bars2 = ax1.bar(x + width/2, co_thresholds, width, label='CO (Current)', 
                    color=SIU_COLORS['primary'], alpha=0.8)
    
    ax1.set_xlabel('Research Intensity Level', fontweight='bold')
    ax1.set_ylabel('Required Publications (5-year)', fontweight='bold')
    ax1.set_title('SIU Research Thresholds: FO vs CO\n(Medium Size Institution)', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(categories)
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # 2. Benefits vs Challenges Matrix
    benefits = ['Academic Credibility', 'Global Recognition', 'Faculty Normalization', 
               'Interdisciplinary Opportunities', 'Comprehensive Profile']
    challenges = ['Higher Thresholds', 'Resource Distribution', 'Performance Expectations', 
                 'Quality Assurance', 'Coordination Complexity']
    
    benefit_scores = [9, 8, 9, 8, 9]  # Impact scores out of 10
    challenge_scores = [7, 6, 8, 7, 6]  # Difficulty scores out of 10
    
    y_pos = np.arange(len(benefits))
    
    # Benefits (positive direction)
    bars_benefits = ax2.barh(y_pos, benefit_scores, color=SIU_COLORS['success'], alpha=0.8, label='Benefits')
    
    # Challenges (negative direction)
    bars_challenges = ax2.barh(y_pos, [-x for x in challenge_scores], color=SIU_COLORS['danger'], alpha=0.8, label='Challenges')
    
    ax2.set_yticks(y_pos)
    ax2.set_yticklabels(benefits)
    ax2.set_xlabel('Impact Score', fontweight='bold')
    ax2.set_title('CO Classification: Benefits vs Challenges\nfor SIU', fontweight='bold')
    ax2.axvline(x=0, color='black', linewidth=0.8)
    ax2.legend()
    ax2.grid(axis='x', alpha=0.3)
    
    # Add challenge labels on the left
    for i, (benefit, challenge) in enumerate(zip(benefits, challenges)):
        ax2.text(-8.5, i, challenge, ha='left', va='center', fontsize=9, style='italic')
    
    # 3. Faculty Area Distribution
    faculty_areas = ['Arts &\nHumanities', 'Engineering &\nTechnology', 
                    'Life Sciences &\nMedicine', 'Natural\nSciences', 
                    'Social Sciences &\nManagement']
    
    # Hypothetical current distribution at SIU
    current_strength = [6, 8, 9, 7, 8]  # Strength scores out of 10
    target_strength = [8, 9, 9, 8, 9]   # Target scores for CO excellence
    
    x = np.arange(len(faculty_areas))
    width = 0.35
    
    bars1 = ax3.bar(x - width/2, current_strength, width, label='Current Strength', 
                    color=SIU_COLORS['neutral'], alpha=0.7)
    bars2 = ax3.bar(x + width/2, target_strength, width, label='Target for CO Excellence', 
                    color=SIU_COLORS['accent'], alpha=0.8)
    
    ax3.set_xlabel('Faculty Areas', fontweight='bold')
    ax3.set_ylabel('Strength Score (1-10)', fontweight='bold')
    ax3.set_title('SIU Faculty Area Development\nCurrent vs Target', fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels(faculty_areas, rotation=45, ha='right')
    ax3.legend()
    ax3.grid(axis='y', alpha=0.3)
    ax3.set_ylim(0, 10)
    
    # 4. Strategic Timeline
    years = ['2025', '2026', '2027', '2028', '2030']
    milestones = ['CO Classification\nAchieved', 'Research Threshold\nMet', 'International\nPartnerships', 
                 'Sustainability\nExcellence', 'Top 500\nGlobal Ranking']
    
    # Progress indicators
    progress = [100, 70, 40, 20, 10]  # Percentage completion
    
    colors_timeline = [SIU_COLORS['success'] if p >= 70 else 
                      SIU_COLORS['warning'] if p >= 40 else 
                      SIU_COLORS['danger'] for p in progress]
    
    bars = ax4.bar(years, progress, color=colors_timeline, alpha=0.8)
    
    ax4.set_xlabel('Timeline', fontweight='bold')
    ax4.set_ylabel('Progress (%)', fontweight='bold')
    ax4.set_title('SIU Strategic Roadmap\nCO Classification Implementation', fontweight='bold')
    ax4.set_ylim(0, 100)
    ax4.grid(axis='y', alpha=0.3)
    
    # Add milestone labels
    for i, (bar, milestone, prog) in enumerate(zip(bars, milestones, progress)):
        ax4.text(bar.get_x() + bar.get_width()/2., prog + 5,
                f'{milestone}\n({prog}%)', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('SIU_CO_Transformation_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_methodology_changes_timeline():
    """Create QS Methodology Evolution Timeline"""
    
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Timeline data
    years = [2015, 2018, 2021, 2024, 2026]
    changes = [
        'Faculty Area\nNormalization\nIntroduced',
        'Enhanced Citation\nProcessing &\nSelf-citation Exclusion',
        'Methodology\nRefinements &\nData Validation',
        'International Research\nNetwork (IRN)\nIntroduced',
        'Employment Outcomes &\nSustainability\nAdded'
    ]
    
    # Create timeline
    y_position = 1
    
    # Draw timeline line
    ax.plot(years, [y_position] * len(years), 'o-', linewidth=3, markersize=10, 
            color=SIU_COLORS['primary'], alpha=0.8)
    
    # Add change descriptions
    for i, (year, change) in enumerate(zip(years, changes)):
        # Alternate positions above and below the line
        y_text = y_position + 0.3 if i % 2 == 0 else y_position - 0.3
        
        # Add text box
        bbox_props = dict(boxstyle="round,pad=0.3", 
                         facecolor=SIU_COLORS['light'], 
                         edgecolor=SIU_COLORS['primary'], 
                         alpha=0.8)
        
        ax.text(year, y_text, f'{year}\n{change}', ha='center', va='center',
                bbox=bbox_props, fontsize=10, fontweight='bold')
        
        # Draw connecting line
        ax.plot([year, year], [y_position, y_text], '--', 
                color=SIU_COLORS['neutral'], alpha=0.6)
    
    # Highlight 2026 changes
    ax.scatter([2026], [y_position], s=200, color=SIU_COLORS['secondary'], 
               alpha=0.8, zorder=5, edgecolors='white', linewidth=2)
    
    ax.set_xlim(2013, 2028)
    ax.set_ylim(0.2, 1.8)
    ax.set_xlabel('Year', fontsize=14, fontweight='bold')
    ax.set_title('QS World University Rankings Methodology Evolution\nKey Changes Timeline', 
                 fontsize=16, fontweight='bold', pad=20)
    
    # Remove y-axis as it's not meaningful
    ax.set_yticks([])
    ax.spines['left'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['top'].set_visible(False)
    
    # Add grid for years
    ax.grid(axis='x', alpha=0.3)
    
    # Add legend
    legend_elements = [
        mpatches.Circle((0, 0), 0.1, facecolor=SIU_COLORS['primary'], label='Methodology Changes'),
        mpatches.Circle((0, 0), 0.1, facecolor=SIU_COLORS['secondary'], label='2026 Major Updates')
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('QS_Methodology_Timeline.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Generate all visualizations for the presentation"""
    print("Generating QS Methodology Visualizations for SIU Leadership Presentation...")
    print("=" * 80)
    
    print("1. Creating QS 2026 Indicators Distribution Chart...")
    create_qs_indicators_chart()
    
    print("2. Creating Institution Classification Comparison...")
    create_classification_comparison()
    
    print("3. Creating SIU Transformation Analysis...")
    create_siu_transformation_chart()
    
    print("4. Creating Methodology Evolution Timeline...")
    create_methodology_changes_timeline()
    
    print("\n" + "=" * 80)
    print("All visualizations generated successfully!")
    print("Files created:")
    print("- QS_2026_Indicators_Distribution.png")
    print("- QS_Classification_Comparison.png") 
    print("- SIU_CO_Transformation_Analysis.png")
    print("- QS_Methodology_Timeline.png")
    print("\nReady for SIU Leadership Presentation!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Advanced PDF Layout Extractor
Author: <PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Description: Advanced PDF content extraction with precise layout preservation
Created: 2025-06-21

This script focuses on:
- Exact layout preservation including spacing, alignment, and formatting
- Table structure maintenance with proper column alignment
- Header/footer detection and preservation
- Text positioning and formatting retention
- Multiple extraction methods for maximum accuracy
"""

import os
import glob
import logging
from pathlib import Path
from datetime import datetime
import re
import traceback

# Import PDF processing libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedPDFLayoutExtractor:
    """
    Advanced PDF extractor that preserves exact layout, formatting, and structure.
    """
    
    def __init__(self, output_filename="QS_WUR_Complete_Content.txt"):
        self.output_filename = output_filename
        self.extraction_stats = {
            'total_files': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_pages': 0,
            'tables_extracted': 0,
            'extraction_methods_used': []
        }
    
    def find_pdf_files(self, directory="."):
        """Find all PDF files in the specified directory."""
        pdf_files = glob.glob(os.path.join(directory, "*.pdf"))
        pdf_files.sort()  # Sort alphabetically for consistent ordering
        logger.info(f"Found {len(pdf_files)} PDF files")
        return pdf_files
    
    def preserve_text_layout(self, text):
        """
        Preserve text layout by maintaining spacing and alignment.
        """
        if not text:
            return ""
        
        # Split into lines and preserve spacing
        lines = text.split('\n')
        processed_lines = []
        
        for line in lines:
            # Preserve leading and trailing spaces
            processed_lines.append(line.rstrip())
        
        return '\n'.join(processed_lines)
    
    def extract_table_with_layout(self, page):
        """
        Extract tables with precise layout preservation using pdfplumber.
        """
        tables_content = []

        try:
            # Try multiple table extraction strategies
            table_settings_list = [
                {
                    "vertical_strategy": "lines",
                    "horizontal_strategy": "lines",
                    "snap_tolerance": 3,
                    "join_tolerance": 3,
                    "edge_min_length": 3,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1,
                    "intersection_tolerance": 3,
                    "text_tolerance": 3,
                    "text_x_tolerance": 3,
                    "text_y_tolerance": 3
                },
                {
                    "vertical_strategy": "text",
                    "horizontal_strategy": "text",
                    "snap_tolerance": 5,
                    "join_tolerance": 5,
                    "edge_min_length": 5,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1,
                    "intersection_tolerance": 5,
                    "text_tolerance": 5,
                    "text_x_tolerance": 5,
                    "text_y_tolerance": 5
                }
            ]

            tables = None
            for settings in table_settings_list:
                try:
                    tables = page.extract_tables(table_settings=settings)
                    if tables and len(tables) > 0:
                        break
                except:
                    continue

            if tables:
                for table_num, table in enumerate(tables, 1):
                    if table and len(table) > 0:
                        tables_content.append(f"\n[TABLE {table_num} - EXTRACTED]")
                        tables_content.append("=" * 60)

                        # Calculate column widths for alignment
                        max_widths = []
                        for row in table:
                            if row:
                                for i, cell in enumerate(row):
                                    cell_text = str(cell) if cell else ""
                                    if i >= len(max_widths):
                                        max_widths.append(len(cell_text))
                                    else:
                                        max_widths[i] = max(max_widths[i], len(cell_text))

                        # Format table with proper alignment
                        for row_idx, row in enumerate(table):
                            if row:
                                formatted_cells = []
                                for i, cell in enumerate(row):
                                    cell_text = str(cell) if cell else ""
                                    if i < len(max_widths):
                                        # Left-align text with proper spacing
                                        formatted_cell = cell_text.ljust(max_widths[i])
                                        formatted_cells.append(formatted_cell)
                                    else:
                                        formatted_cells.append(cell_text)

                                # Join cells with proper spacing
                                table_row = " | ".join(formatted_cells)
                                tables_content.append(table_row)

                                # Add separator line after header (first row)
                                if row_idx == 0:
                                    separator_parts = []
                                    for width in max_widths:
                                        separator_parts.append("-" * width)
                                    separator = " | ".join(separator_parts)
                                    tables_content.append(separator)

                        tables_content.append("=" * 60)
                        tables_content.append("[END TABLE]\n")
                        self.extraction_stats['tables_extracted'] += 1

        except Exception as e:
            logger.warning(f"Table extraction failed: {str(e)}")

        return '\n'.join(tables_content)
    
    def extract_with_advanced_pdfplumber(self, pdf_path):
        """
        Advanced extraction using pdfplumber with layout preservation.
        """
        if not PDFPLUMBER_AVAILABLE:
            return None, "pdfplumber not available"
        
        try:
            content_parts = []
            
            with pdfplumber.open(pdf_path) as pdf:
                logger.info(f"Processing {len(pdf.pages)} pages with advanced pdfplumber")
                
                for page_num, page in enumerate(pdf.pages, 1):
                    content_parts.append(f"\n{'='*80}")
                    content_parts.append(f"PAGE {page_num}")
                    content_parts.append(f"{'='*80}")
                    
                    # Get page dimensions for context
                    page_width = page.width
                    page_height = page.height
                    content_parts.append(f"Page dimensions: {page_width:.1f} x {page_height:.1f}")
                    content_parts.append("")
                    
                    # Extract text with layout preservation
                    page_text = page.extract_text(layout=True, x_tolerance=3, y_tolerance=3)
                    
                    if page_text:
                        # Preserve the layout
                        preserved_text = self.preserve_text_layout(page_text)
                        content_parts.append(preserved_text)
                    
                    # Extract tables separately with better formatting
                    table_content = self.extract_table_with_layout(page)
                    if table_content.strip():
                        content_parts.append("\n" + table_content)
                    
                    # Add page separator
                    content_parts.append(f"\n{'='*80}")
                    content_parts.append("")
            
            return '\n'.join(content_parts), "success"
        
        except Exception as e:
            logger.error(f"Advanced pdfplumber extraction failed: {str(e)}")
            return None, f"Advanced pdfplumber error: {str(e)}"
    
    def extract_with_pymupdf_layout(self, pdf_path):
        """
        Extract with PyMuPDF focusing on layout preservation.
        """
        if not PYMUPDF_AVAILABLE:
            return None, "PyMuPDF not available"
        
        try:
            content_parts = []
            doc = fitz.open(pdf_path)
            logger.info(f"Processing {len(doc)} pages with PyMuPDF layout preservation")
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                content_parts.append(f"\n{'='*80}")
                content_parts.append(f"PAGE {page_num + 1}")
                content_parts.append(f"{'='*80}")
                
                # Get page dimensions
                rect = page.rect
                content_parts.append(f"Page dimensions: {rect.width:.1f} x {rect.height:.1f}")
                content_parts.append("")
                
                # Extract text with layout preservation
                text_dict = page.get_text("dict")
                
                # Process text blocks to maintain layout
                for block in text_dict["blocks"]:
                    if "lines" in block:  # Text block
                        for line in block["lines"]:
                            line_text = ""
                            for span in line["spans"]:
                                line_text += span["text"]
                            if line_text.strip():
                                content_parts.append(line_text)
                
                # Try to extract tables using get_text with layout
                page_text = page.get_text(sort=True)
                if page_text:
                    # Look for table-like structures
                    lines = page_text.split('\n')
                    table_lines = []
                    in_table = False
                    
                    for line in lines:
                        # Simple heuristic to detect table rows (multiple spaces or tabs)
                        if re.search(r'\s{3,}|\t', line) and len(line.split()) > 2:
                            if not in_table:
                                content_parts.append("\n[DETECTED TABLE STRUCTURE]")
                                in_table = True
                            # Format table-like content
                            formatted_line = re.sub(r'\s{2,}', ' | ', line.strip())
                            content_parts.append(formatted_line)
                        else:
                            if in_table:
                                content_parts.append("[END TABLE STRUCTURE]\n")
                                in_table = False
                
                content_parts.append(f"\n{'='*80}")
                content_parts.append("")
            
            doc.close()
            return '\n'.join(content_parts), "success"
        
        except Exception as e:
            logger.error(f"PyMuPDF layout extraction failed: {str(e)}")
            return None, f"PyMuPDF layout error: {str(e)}"
    
    def extract_pdf_content_with_layout(self, pdf_path):
        """
        Extract content with advanced layout preservation.
        """
        filename = os.path.basename(pdf_path)
        logger.info(f"Extracting content with layout preservation from: {filename}")
        
        # Try advanced pdfplumber first (best for layout)
        content, status = self.extract_with_advanced_pdfplumber(pdf_path)
        if content and status == "success":
            logger.info(f"Successfully extracted content using advanced pdfplumber")
            if "pdfplumber" not in self.extraction_stats['extraction_methods_used']:
                self.extraction_stats['extraction_methods_used'].append("pdfplumber")
            return content, "advanced_pdfplumber"
        
        # Fallback to PyMuPDF with layout
        content, status = self.extract_with_pymupdf_layout(pdf_path)
        if content and status == "success":
            logger.info(f"Successfully extracted content using PyMuPDF layout")
            if "PyMuPDF" not in self.extraction_stats['extraction_methods_used']:
                self.extraction_stats['extraction_methods_used'].append("PyMuPDF")
            return content, "pymupdf_layout"
        
        logger.error(f"All layout extraction methods failed for {filename}")
        return None, "all_methods_failed"
    
    def process_all_pdfs_with_layout(self, directory="."):
        """
        Process all PDF files with advanced layout preservation.
        """
        pdf_files = self.find_pdf_files(directory)
        self.extraction_stats['total_files'] = len(pdf_files)
        
        if not pdf_files:
            logger.warning("No PDF files found in the directory")
            return False
        
        # Create the combined content
        content_parts = []
        
        # Add comprehensive header
        content_parts.append("=" * 100)
        content_parts.append("QS WORLD UNIVERSITY RANKINGS - COMPLETE GUIDELINES AND DOCUMENTATION")
        content_parts.append("=" * 100)
        content_parts.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_parts.append(f"Total PDF files processed: {len(pdf_files)}")
        content_parts.append(f"Extraction method: Advanced layout preservation")
        content_parts.append(f"Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        content_parts.append("=" * 100)
        content_parts.append("")
        
        # Process each PDF file
        for pdf_index, pdf_path in enumerate(pdf_files, 1):
            filename = os.path.basename(pdf_path)
            logger.info(f"Processing {pdf_index}/{len(pdf_files)}: {filename}")
            
            # Add file header
            content_parts.append("\n" + "#" * 100)
            content_parts.append(f"DOCUMENT {pdf_index}: {filename}")
            content_parts.append("#" * 100)
            content_parts.append(f"Source File: {filename}")
            content_parts.append(f"Full Path: {pdf_path}")
            content_parts.append(f"Processing Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content_parts.append("#" * 100)
            
            # Extract content with layout preservation
            content, method = self.extract_pdf_content_with_layout(pdf_path)
            
            if content:
                content_parts.append(content)
                self.extraction_stats['successful_extractions'] += 1
                logger.info(f"Successfully processed {filename} using {method}")
            else:
                error_content = f"""
ERROR: FAILED TO EXTRACT CONTENT FROM {filename}
{'='*60}
All extraction methods failed for this file.
Please check the file manually or try alternative tools.
File may be corrupted, password-protected, or use unsupported formatting.
{'='*60}
"""
                content_parts.append(error_content)
                self.extraction_stats['failed_extractions'] += 1
                logger.error(f"Failed to process {filename}")
            
            # Add document separator
            content_parts.append("\n" + "#" * 100)
            content_parts.append("")
        
        # Add final statistics
        content_parts.append("\n" + "=" * 100)
        content_parts.append("EXTRACTION STATISTICS AND SUMMARY")
        content_parts.append("=" * 100)
        content_parts.append(f"Total files found: {self.extraction_stats['total_files']}")
        content_parts.append(f"Successfully extracted: {self.extraction_stats['successful_extractions']}")
        content_parts.append(f"Failed extractions: {self.extraction_stats['failed_extractions']}")
        content_parts.append(f"Tables extracted: {self.extraction_stats['tables_extracted']}")
        content_parts.append(f"Extraction methods used: {', '.join(self.extraction_stats['extraction_methods_used'])}")
        content_parts.append(f"Success rate: {(self.extraction_stats['successful_extractions']/self.extraction_stats['total_files']*100):.1f}%")
        content_parts.append("")
        content_parts.append("Generated by: Advanced PDF Layout Extractor")
        content_parts.append("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        content_parts.append("Contact: <EMAIL> / <EMAIL>")
        content_parts.append("=" * 100)
        
        # Write to file
        try:
            final_content = '\n'.join(content_parts)
            with open(self.output_filename, 'w', encoding='utf-8') as f:
                f.write(final_content)
            
            logger.info(f"Successfully created {self.output_filename}")
            logger.info(f"Final file size: {len(final_content)} characters")
            logger.info(f"Extraction Statistics: {self.extraction_stats}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write output file: {str(e)}")
            return False

def main():
    """Main function to run the advanced PDF extraction process."""
    print("=" * 100)
    print("ADVANCED PDF LAYOUT EXTRACTOR")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("Focus: Exact layout preservation with table structure maintenance")
    print("=" * 100)
    
    # Initialize extractor
    extractor = AdvancedPDFLayoutExtractor("QS_WUR_Complete_Content.txt")
    
    # Process all PDFs with advanced layout preservation
    success = extractor.process_all_pdfs_with_layout()
    
    if success:
        print(f"\n✅ Successfully created {extractor.output_filename}")
        print(f"📊 Detailed Extraction Statistics:")
        for key, value in extractor.extraction_stats.items():
            print(f"   - {key}: {value}")
        print(f"\n📄 Output file ready for AI tools and analysis")
    else:
        print("\n❌ Failed to create the complete content file")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

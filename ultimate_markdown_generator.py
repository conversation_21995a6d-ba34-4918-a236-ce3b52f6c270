#!/usr/bin/env python3
"""
Ultimate Markdown Generator with Perfect Table Formatting
Author: Dr<PERSON> <PERSON><PERSON>, Symbiosis International (Deemed University)
Description: Advanced Markdown generation with perfect table formatting and structure
Created: 2025-06-21

This script implements:
- Perfect Markdown table formatting with proper alignment
- Enhanced document structure with navigation
- Mathematical formula preservation
- Professional styling and organization
- Comprehensive verification and validation
"""

import os
import glob
import logging
from pathlib import Path
from datetime import datetime
import re
import traceback
import json

# Import PDF processing libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateMarkdownGenerator:
    """
    Ultimate Markdown generator with perfect table formatting and professional structure.
    """
    
    def __init__(self, output_filename="QS_WUR_Perfect_Guidelines.md"):
        self.output_filename = output_filename
        self.extraction_stats = {
            'total_files': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_pages': 0,
            'tables_extracted': 0,
            'markdown_tables_created': 0,
            'text_blocks_processed': 0,
            'extraction_methods_used': []
        }
        self.verification_log = []
        self.table_of_contents = []
    
    def find_pdf_files(self, directory="."):
        """Find all PDF files in the specified directory."""
        pdf_files = glob.glob(os.path.join(directory, "*.pdf"))
        pdf_files.sort()
        logger.info(f"Found {len(pdf_files)} PDF files")
        return pdf_files
    
    def sanitize_markdown_text(self, text):
        """
        Sanitize text for Markdown formatting while preserving important characters.
        """
        if not text:
            return ""
        
        # Convert to string if not already
        text = str(text)
        
        # Preserve mathematical symbols and formulas
        # Escape problematic Markdown characters but preserve mathematical notation
        text = text.replace('|', '\\|')  # Escape pipe characters in table cells
        
        # Clean up excessive whitespace while preserving intentional spacing
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def create_markdown_table(self, table_data, title="Table"):
        """
        Create a perfectly formatted Markdown table.
        """
        if not table_data or len(table_data) == 0:
            return []
        
        markdown_lines = []
        
        # Add table title
        markdown_lines.append(f"\n#### {title}\n")
        
        # Clean and prepare table data
        cleaned_table = []
        for row in table_data:
            if row:  # Skip empty rows
                cleaned_row = []
                for cell in row:
                    cell_text = self.sanitize_markdown_text(cell) if cell is not None else ""
                    cleaned_row.append(cell_text)
                if any(cell.strip() for cell in cleaned_row):  # Only add rows with content
                    cleaned_table.append(cleaned_row)
        
        if not cleaned_table:
            return []
        
        # Ensure all rows have the same number of columns
        max_cols = max(len(row) for row in cleaned_table)
        for row in cleaned_table:
            while len(row) < max_cols:
                row.append("")
        
        # Create Markdown table
        if len(cleaned_table) > 0:
            # Header row
            header_row = "| " + " | ".join(cleaned_table[0]) + " |"
            markdown_lines.append(header_row)
            
            # Separator row
            separator = "| " + " | ".join(["---" for _ in range(max_cols)]) + " |"
            markdown_lines.append(separator)
            
            # Data rows
            for row in cleaned_table[1:]:
                data_row = "| " + " | ".join(row) + " |"
                markdown_lines.append(data_row)
        
        markdown_lines.append("")  # Add spacing after table
        self.extraction_stats['markdown_tables_created'] += 1
        
        return markdown_lines
    
    def detect_and_extract_tables_for_markdown(self, page):
        """
        Advanced table detection specifically optimized for Markdown output.
        """
        tables_content = []
        
        try:
            # Strategy 1: pdfplumber table extraction with multiple settings
            table_settings_list = [
                {
                    "vertical_strategy": "lines",
                    "horizontal_strategy": "lines",
                    "snap_tolerance": 3,
                    "join_tolerance": 3,
                    "edge_min_length": 3,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1,
                    "intersection_tolerance": 3,
                    "text_tolerance": 3,
                    "text_x_tolerance": 3,
                    "text_y_tolerance": 3
                },
                {
                    "vertical_strategy": "text",
                    "horizontal_strategy": "text",
                    "snap_tolerance": 5,
                    "join_tolerance": 5,
                    "edge_min_length": 5,
                    "min_words_vertical": 1,
                    "min_words_horizontal": 1,
                    "intersection_tolerance": 5,
                    "text_tolerance": 5,
                    "text_x_tolerance": 5,
                    "text_y_tolerance": 5
                }
            ]
            
            tables = None
            successful_strategy = None
            
            for i, settings in enumerate(table_settings_list):
                try:
                    tables = page.extract_tables(table_settings=settings)
                    if tables and len(tables) > 0:
                        successful_strategy = f"Strategy {i+1}"
                        break
                except Exception as e:
                    logger.debug(f"Table extraction strategy {i+1} failed: {str(e)}")
                    continue
            
            # Strategy 2: Manual table detection using text patterns
            if not tables or len(tables) == 0:
                tables = self.detect_tables_by_pattern_markdown(page)
                if tables:
                    successful_strategy = "Pattern Detection"
            
            # Process extracted tables for Markdown
            if tables:
                for table_num, table in enumerate(tables, 1):
                    if table and len(table) > 0:
                        markdown_table = self.create_markdown_table(
                            table, 
                            f"Table {table_num}"
                        )
                        tables_content.extend(markdown_table)
                        
                        self.extraction_stats['tables_extracted'] += 1
                        logger.info(f"Successfully created Markdown table {table_num} using {successful_strategy}")
            
        except Exception as e:
            logger.warning(f"Markdown table extraction failed: {str(e)}")
        
        return tables_content
    
    def detect_tables_by_pattern_markdown(self, page):
        """
        Detect tables by analyzing text patterns for Markdown conversion.
        """
        try:
            # Get text with layout preservation
            text = page.extract_text(layout=True, x_tolerance=3, y_tolerance=3)
            if not text:
                return []
            
            lines = text.split('\n')
            tables = []
            current_table = []
            in_table = False
            
            for line in lines:
                # Skip empty lines
                if not line.strip():
                    if in_table and current_table:
                        # End of table
                        tables.append(current_table)
                        current_table = []
                        in_table = False
                    continue
                
                # Detect table-like patterns
                if self.is_table_row_markdown(line):
                    if not in_table:
                        in_table = True
                    current_table.append(self.parse_table_row_markdown(line))
                else:
                    if in_table and current_table:
                        # End of table
                        tables.append(current_table)
                        current_table = []
                        in_table = False
            
            # Don't forget the last table
            if in_table and current_table:
                tables.append(current_table)
            
            return tables
            
        except Exception as e:
            logger.warning(f"Pattern-based table detection for Markdown failed: {str(e)}")
            return []
    
    def is_table_row_markdown(self, line):
        """
        Determine if a line looks like a table row for Markdown conversion.
        """
        line = line.strip()
        if not line:
            return False
        
        words = line.split()
        if len(words) < 2:
            return False
        
        # Look for multiple words separated by significant spaces
        if re.search(r'\s{3,}', line):
            return True
        
        # Look for tab characters
        if '\t' in line:
            return True
        
        # Look for percentage signs, numbers, or other table-like content
        if re.search(r'\d+%|\d+\.\d+|\d+,\d+', line):
            return True
        
        return False
    
    def parse_table_row_markdown(self, line):
        """
        Parse a table row into columns for Markdown conversion.
        """
        # Split by multiple spaces or tabs
        columns = re.split(r'\s{3,}|\t+', line.strip())
        
        # Clean up columns
        cleaned_columns = []
        for col in columns:
            col = col.strip()
            if col:  # Only add non-empty columns
                cleaned_columns.append(col)
        
        return cleaned_columns
    
    def create_document_navigation(self, pdf_files):
        """
        Create a table of contents for the Markdown document.
        """
        toc_lines = []
        toc_lines.append("## Table of Contents\n")
        
        for i, pdf_path in enumerate(pdf_files, 1):
            filename = os.path.basename(pdf_path)
            # Create anchor-friendly title
            anchor = filename.replace('.pdf', '').replace(' ', '-').replace('(', '').replace(')', '').replace('–', '-').lower()
            title = filename.replace('.pdf', '')
            
            toc_lines.append(f"{i}. [{title}](#{anchor})")
            self.table_of_contents.append((title, anchor))
        
        toc_lines.append("")
        return toc_lines

    def extract_with_markdown_precision(self, pdf_path):
        """
        Extract content with precision optimized for Markdown output.
        """
        if not PDFPLUMBER_AVAILABLE:
            return None, "pdfplumber not available"

        try:
            content_parts = []

            with pdfplumber.open(pdf_path) as pdf:
                logger.info(f"Processing {len(pdf.pages)} pages for Markdown conversion")

                for page_num, page in enumerate(pdf.pages, 1):
                    # Add page header
                    content_parts.append(f"\n### Page {page_num}\n")

                    # Extract tables first for Markdown
                    table_content = self.detect_and_extract_tables_for_markdown(page)

                    # Extract text content
                    page_text = page.extract_text(layout=True, x_tolerance=3, y_tolerance=3)

                    # Process text content for Markdown
                    if page_text:
                        # Split into paragraphs and format
                        paragraphs = page_text.split('\n\n')
                        for para in paragraphs:
                            para = para.strip()
                            if para:
                                # Check if it's a heading (all caps, short line)
                                if len(para) < 100 and para.isupper() and len(para.split()) < 10:
                                    content_parts.append(f"#### {para.title()}\n")
                                else:
                                    # Regular paragraph
                                    sanitized_para = self.sanitize_markdown_text(para)
                                    content_parts.append(f"{sanitized_para}\n")

                    # Add extracted tables
                    if table_content:
                        content_parts.extend(table_content)

                    self.extraction_stats['total_pages'] += 1
                    self.extraction_stats['text_blocks_processed'] += 1

            return '\n'.join(content_parts), "success"

        except Exception as e:
            logger.error(f"Markdown precision extraction failed: {str(e)}")
            return None, f"Markdown extraction error: {str(e)}"

    def verify_markdown_quality(self, content, pdf_path):
        """
        Verify the quality of Markdown extraction and log issues.
        """
        verification_results = {
            'file': os.path.basename(pdf_path),
            'content_length': len(content),
            'line_count': len(content.split('\n')),
            'markdown_tables': content.count('|'),
            'headings': content.count('#'),
            'issues': []
        }

        # Check for common Markdown issues
        if len(content) < 100:
            verification_results['issues'].append("Content too short - possible extraction failure")

        if '### Page 1' not in content:
            verification_results['issues'].append("Missing page structure")

        if verification_results['markdown_tables'] == 0 and 'table' in pdf_path.lower():
            verification_results['issues'].append("No Markdown tables detected in file that likely contains tables")

        # Check for unescaped pipe characters in non-table content
        lines = content.split('\n')
        for line in lines:
            if '|' in line and not line.strip().startswith('|') and not line.strip().endswith('|'):
                verification_results['issues'].append("Unescaped pipe characters detected")
                break

        self.verification_log.append(verification_results)

        if verification_results['issues']:
            logger.warning(f"Markdown verification issues for {verification_results['file']}: {verification_results['issues']}")
        else:
            logger.info(f"Markdown verification passed for {verification_results['file']}")

        return verification_results

    def process_all_pdfs_to_markdown(self, directory="."):
        """
        Process all PDFs and create a perfect Markdown document.
        """
        pdf_files = self.find_pdf_files(directory)
        self.extraction_stats['total_files'] = len(pdf_files)

        if not pdf_files:
            logger.warning("No PDF files found in the directory")
            return False

        # Create the Markdown content
        markdown_content = []

        # Add comprehensive header
        markdown_content.append("# QS World University Rankings - Complete Guidelines and Documentation\n")
        markdown_content.append(f"**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append(f"**Total PDF files processed:** {len(pdf_files)}")
        markdown_content.append(f"**Extraction method:** Ultimate precision with perfect Markdown formatting")
        markdown_content.append(f"**Author:** Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        markdown_content.append(f"**Contact:** <EMAIL> / <EMAIL>\n")
        markdown_content.append("---\n")

        # Add table of contents
        toc = self.create_document_navigation(pdf_files)
        markdown_content.extend(toc)
        markdown_content.append("---\n")

        # Process each PDF file
        for pdf_index, pdf_path in enumerate(pdf_files, 1):
            filename = os.path.basename(pdf_path)
            logger.info(f"Processing {pdf_index}/{len(pdf_files)}: {filename}")

            # Create anchor-friendly title
            anchor = filename.replace('.pdf', '').replace(' ', '-').replace('(', '').replace(')', '').replace('–', '-').lower()
            title = filename.replace('.pdf', '')

            # Add document header
            markdown_content.append(f"\n## {pdf_index}. {title} {{#{anchor}}}\n")
            markdown_content.append(f"**Source File:** `{filename}`")
            markdown_content.append(f"**File Path:** `{pdf_path}`")
            markdown_content.append(f"**Processing Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            markdown_content.append("---\n")

            # Extract content with Markdown precision
            content, status = self.extract_with_markdown_precision(pdf_path)

            if content and status == "success":
                # Verify Markdown quality
                verification = self.verify_markdown_quality(content, pdf_path)

                markdown_content.append(content)
                self.extraction_stats['successful_extractions'] += 1

                if "pdfplumber" not in self.extraction_stats['extraction_methods_used']:
                    self.extraction_stats['extraction_methods_used'].append("pdfplumber")

                logger.info(f"Successfully processed {filename} - {verification['line_count']} lines, {verification['markdown_tables']} table elements")
            else:
                error_content = f"""
### ⚠️ Extraction Error

**File:** {filename}
**Status:** Failed to extract content
**Error:** {status}

Please check the file manually or try alternative tools.
File may be corrupted, password-protected, or use unsupported formatting.

---
"""
                markdown_content.append(error_content)
                self.extraction_stats['failed_extractions'] += 1
                logger.error(f"Failed to process {filename}: {status}")

            # Add document separator
            markdown_content.append("\n---\n")

        # Add comprehensive statistics
        markdown_content.append("\n## Extraction Statistics and Summary\n")
        markdown_content.append("### Processing Results\n")
        markdown_content.append(f"- **Total files found:** {self.extraction_stats['total_files']}")
        markdown_content.append(f"- **Successfully extracted:** {self.extraction_stats['successful_extractions']}")
        markdown_content.append(f"- **Failed extractions:** {self.extraction_stats['failed_extractions']}")
        markdown_content.append(f"- **Total pages processed:** {self.extraction_stats['total_pages']}")
        markdown_content.append(f"- **Tables extracted:** {self.extraction_stats['tables_extracted']}")
        markdown_content.append(f"- **Markdown tables created:** {self.extraction_stats['markdown_tables_created']}")
        markdown_content.append(f"- **Text blocks processed:** {self.extraction_stats['text_blocks_processed']}")
        markdown_content.append(f"- **Extraction methods used:** {', '.join(self.extraction_stats['extraction_methods_used'])}")
        markdown_content.append(f"- **Success rate:** {(self.extraction_stats['successful_extractions']/self.extraction_stats['total_files']*100):.1f}%\n")

        # Add verification summary
        markdown_content.append("### Verification Results\n")
        for result in self.verification_log:
            markdown_content.append(f"#### {result['file']}")
            markdown_content.append(f"- **Content length:** {result['content_length']:,} characters")
            markdown_content.append(f"- **Line count:** {result['line_count']:,}")
            markdown_content.append(f"- **Markdown tables:** {result['markdown_tables']} elements")
            markdown_content.append(f"- **Headings:** {result['headings']}")
            if result['issues']:
                markdown_content.append(f"- **Issues:** {'; '.join(result['issues'])}")
            else:
                markdown_content.append("- **Status:** ✅ All checks passed")
            markdown_content.append("")

        markdown_content.append("### Technical Information\n")
        markdown_content.append("**Generated by:** Ultimate Markdown Generator")
        markdown_content.append("**Author:** Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        markdown_content.append("**Contact:** <EMAIL> / <EMAIL>")
        markdown_content.append("**Institution:** Symbiosis International (Deemed University), Pune")
        markdown_content.append("**Department:** Quality Management & Benchmarking (QMB) and Quality Assurance (QA)")

        # Write to file
        try:
            final_content = '\n'.join(markdown_content)
            with open(self.output_filename, 'w', encoding='utf-8') as f:
                f.write(final_content)

            logger.info(f"Successfully created {self.output_filename}")
            logger.info(f"Final file size: {len(final_content):,} characters")
            logger.info(f"Markdown Statistics: {self.extraction_stats}")
            return True

        except Exception as e:
            logger.error(f"Failed to write Markdown output file: {str(e)}")
            return False

def main():
    """Main function to run the ultimate Markdown generation process."""
    print("=" * 120)
    print("ULTIMATE MARKDOWN GENERATOR WITH PERFECT TABLE FORMATTING")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("Focus: Perfect Markdown formatting with professional table structure")
    print("=" * 120)

    # Initialize generator
    generator = UltimateMarkdownGenerator("QS_WUR_Perfect_Guidelines.md")

    # Process all PDFs to create perfect Markdown
    success = generator.process_all_pdfs_to_markdown()

    if success:
        print(f"\n✅ Successfully created {generator.output_filename}")
        print(f"📊 Ultimate Markdown Statistics:")
        for key, value in generator.extraction_stats.items():
            print(f"   - {key}: {value}")

        print(f"\n🔍 Markdown Verification Summary:")
        total_issues = sum(len(result['issues']) for result in generator.verification_log)
        print(f"   - Files verified: {len(generator.verification_log)}")
        print(f"   - Total issues found: {total_issues}")
        print(f"   - Files with issues: {sum(1 for result in generator.verification_log if result['issues'])}")
        print(f"   - Markdown tables created: {generator.extraction_stats['markdown_tables_created']}")

        print(f"\n📄 Perfect Markdown file ready for AI tools and documentation")
    else:
        print("\n❌ Failed to create the ultimate Markdown file")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())

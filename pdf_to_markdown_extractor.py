#!/usr/bin/env python3
"""
PDF to Markdown Extractor
Author: Dr<PERSON> <PERSON><PERSON>, Symbiosis International (Deemed University)
Description: Extracts content from all PDF files in the current directory and combines them into a single Markdown file
Created: 2025-06-21

This script uses multiple PDF processing libraries to ensure maximum accuracy and completeness:
- pdfplumber: For high-quality text extraction with layout preservation
- PyMuPDF (fitz): For fallback extraction and metadata
- PyPDF2: For additional fallback support
"""

import os
import glob
import logging
from pathlib import Path
from datetime import datetime
import traceback

# Import PDF processing libraries
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False
    print("Warning: pdfplumber not available")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("Warning: PyMuPDF not available")

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    print("Warning: PyPDF2 not available")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFToMarkdownExtractor:
    """
    Comprehensive PDF to Markdown extractor that preserves layout and formatting.
    Uses multiple extraction methods for maximum accuracy and completeness.
    """
    
    def __init__(self, output_filename="combined_pdfs_content.md"):
        self.output_filename = output_filename
        self.extraction_stats = {
            'total_files': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_pages': 0,
            'extraction_methods_used': []
        }
    
    def find_pdf_files(self, directory="."):
        """Find all PDF files in the specified directory."""
        pdf_files = glob.glob(os.path.join(directory, "*.pdf"))
        pdf_files.sort()  # Sort alphabetically for consistent ordering
        logger.info(f"Found {len(pdf_files)} PDF files")
        return pdf_files
    
    def extract_with_pdfplumber(self, pdf_path):
        """
        Extract text using pdfplumber - best for layout preservation.
        """
        if not PDFPLUMBER_AVAILABLE:
            return None, "pdfplumber not available"
        
        try:
            text_content = []
            with pdfplumber.open(pdf_path) as pdf:
                logger.info(f"Processing {len(pdf.pages)} pages with pdfplumber")
                for page_num, page in enumerate(pdf.pages, 1):
                    # Extract text with layout preservation
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append(f"\n--- Page {page_num} ---\n")
                        text_content.append(page_text)
                    
                    # Try to extract tables if present
                    tables = page.extract_tables()
                    if tables:
                        for table_num, table in enumerate(tables, 1):
                            text_content.append(f"\n**Table {table_num} on Page {page_num}:**\n")
                            # Convert table to markdown format
                            if table and len(table) > 0:
                                # Create markdown table
                                for row_idx, row in enumerate(table):
                                    if row:  # Skip empty rows
                                        row_text = " | ".join([str(cell) if cell else "" for cell in row])
                                        text_content.append(f"| {row_text} |")
                                        # Add separator after header row
                                        if row_idx == 0:
                                            separator = " | ".join(["---" for _ in row])
                                            text_content.append(f"| {separator} |")
                                text_content.append("")
            
            return "\n".join(text_content), "success"
        
        except Exception as e:
            logger.error(f"pdfplumber extraction failed: {str(e)}")
            return None, f"pdfplumber error: {str(e)}"
    
    def extract_with_pymupdf(self, pdf_path):
        """
        Extract text using PyMuPDF - good fallback option.
        """
        if not PYMUPDF_AVAILABLE:
            return None, "PyMuPDF not available"
        
        try:
            text_content = []
            doc = fitz.open(pdf_path)
            logger.info(f"Processing {len(doc)} pages with PyMuPDF")
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_text = page.get_text()
                if page_text.strip():
                    text_content.append(f"\n--- Page {page_num + 1} ---\n")
                    text_content.append(page_text)
            
            doc.close()
            return "\n".join(text_content), "success"
        
        except Exception as e:
            logger.error(f"PyMuPDF extraction failed: {str(e)}")
            return None, f"PyMuPDF error: {str(e)}"
    
    def extract_with_pypdf2(self, pdf_path):
        """
        Extract text using PyPDF2 - basic fallback option.
        """
        if not PYPDF2_AVAILABLE:
            return None, "PyPDF2 not available"
        
        try:
            text_content = []
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                logger.info(f"Processing {len(pdf_reader.pages)} pages with PyPDF2")
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"\n--- Page {page_num} ---\n")
                        text_content.append(page_text)
            
            return "\n".join(text_content), "success"
        
        except Exception as e:
            logger.error(f"PyPDF2 extraction failed: {str(e)}")
            return None, f"PyPDF2 error: {str(e)}"
    
    def extract_pdf_content(self, pdf_path):
        """
        Extract content from a single PDF using the best available method.
        Tries multiple extraction methods in order of preference.
        """
        filename = os.path.basename(pdf_path)
        logger.info(f"Extracting content from: {filename}")
        
        # Try extraction methods in order of preference
        extraction_methods = [
            ("pdfplumber", self.extract_with_pdfplumber),
            ("PyMuPDF", self.extract_with_pymupdf),
            ("PyPDF2", self.extract_with_pypdf2)
        ]
        
        for method_name, extraction_func in extraction_methods:
            content, status = extraction_func(pdf_path)
            if content and status == "success":
                logger.info(f"Successfully extracted content using {method_name}")
                if method_name not in self.extraction_stats['extraction_methods_used']:
                    self.extraction_stats['extraction_methods_used'].append(method_name)
                return content, method_name
        
        logger.error(f"All extraction methods failed for {filename}")
        return None, "all_methods_failed"
    
    def process_all_pdfs(self, directory="."):
        """
        Process all PDF files in the directory and combine into a single Markdown file.
        """
        pdf_files = self.find_pdf_files(directory)
        self.extraction_stats['total_files'] = len(pdf_files)
        
        if not pdf_files:
            logger.warning("No PDF files found in the directory")
            return False
        
        # Create the combined markdown content
        markdown_content = []
        
        # Add header
        markdown_content.append("# Combined PDF Content - QS World University Rankings Guidelines")
        markdown_content.append(f"\n**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        markdown_content.append(f"**Total PDF files processed:** {len(pdf_files)}")
        markdown_content.append("\n---\n")
        
        # Process each PDF file
        for pdf_path in pdf_files:
            filename = os.path.basename(pdf_path)
            logger.info(f"Processing: {filename}")
            
            # Add file header
            markdown_content.append(f"\n# {filename}")
            markdown_content.append(f"\n**Source File:** `{filename}`")
            markdown_content.append(f"**File Path:** `{pdf_path}`")
            markdown_content.append("\n---\n")
            
            # Extract content
            content, method = self.extract_pdf_content(pdf_path)
            
            if content:
                markdown_content.append(content)
                self.extraction_stats['successful_extractions'] += 1
                logger.info(f"Successfully processed {filename} using {method}")
            else:
                error_msg = f"**ERROR: Failed to extract content from {filename}**\n"
                error_msg += f"All extraction methods failed. Please check the file manually.\n"
                markdown_content.append(error_msg)
                self.extraction_stats['failed_extractions'] += 1
                logger.error(f"Failed to process {filename}")
            
            # Add separator between files
            markdown_content.append("\n\n" + "="*80 + "\n\n")
        
        # Add extraction statistics at the end
        markdown_content.append("\n# Extraction Statistics")
        markdown_content.append(f"\n- **Total files found:** {self.extraction_stats['total_files']}")
        markdown_content.append(f"- **Successfully extracted:** {self.extraction_stats['successful_extractions']}")
        markdown_content.append(f"- **Failed extractions:** {self.extraction_stats['failed_extractions']}")
        markdown_content.append(f"- **Extraction methods used:** {', '.join(self.extraction_stats['extraction_methods_used'])}")
        markdown_content.append(f"\n**Generated by:** PDF to Markdown Extractor")
        markdown_content.append(f"**Author:** Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
        
        # Write to file
        try:
            with open(self.output_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))
            
            logger.info(f"Successfully created {self.output_filename}")
            logger.info(f"Extraction Statistics: {self.extraction_stats}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to write output file: {str(e)}")
            return False

def main():
    """Main function to run the PDF extraction process."""
    print("="*80)
    print("PDF to Markdown Extractor")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("="*80)
    
    # Initialize extractor
    extractor = PDFToMarkdownExtractor("QS_WUR_Combined_Guidelines.md")
    
    # Process all PDFs
    success = extractor.process_all_pdfs()
    
    if success:
        print(f"\n✅ Successfully created {extractor.output_filename}")
        print(f"📊 Extraction Statistics:")
        for key, value in extractor.extraction_stats.items():
            print(f"   - {key}: {value}")
    else:
        print("\n❌ Failed to create the combined markdown file")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
